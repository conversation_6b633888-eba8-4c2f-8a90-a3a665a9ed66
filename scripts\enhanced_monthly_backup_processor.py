#!/usr/bin/env python3
"""
Enhanced Monthly Backup Processor for TNGD - Production Ready

CRITICAL FIXES IMPLEMENTED:
- Automatic retry mechanism with exponential backoff
- Dynamic resource management and throttling
- Intelligent failure analysis and recovery
- Real-time system monitoring
- Enhanced error handling and logging
- Dependency chain health checks

This processor addresses all critical production readiness issues:
1. Robust error recovery with automatic retries
2. Resource management with adaptive throttling
3. Configuration resilience with fallbacks
4. Comprehensive monitoring and alerting
5. Intelligent failure analysis

Features:
- Multi-level retry mechanism (day-level and table-level)
- Dynamic delay adjustment based on system load
- Automatic resource throttling when thresholds exceeded
- Detailed failure analysis with root cause identification
- Real-time progress monitoring with alerts
- Checkpoint-based recovery for interrupted operations
- Health checks before and during operations
"""

import sys
import os
import time
import json
import argparse
import datetime
import psutil
import threading
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import project modules
from core.config_manager import ConfigManager
from core.monthly_backup_processor import MonthlyBackupProcessor
from core.monthly_backup_config import MonthlyBackupConfigManager
from utils.minimal_logging import logger
from utils.error_handler import handle_error, safe_execute
from utils.notification import send_backup_summary_notification


class BackupStatus(Enum):
    """Enhanced backup status tracking."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"
    RECOVERED = "recovered"
    CRITICAL_FAILURE = "critical_failure"


@dataclass
class DayBackupResult:
    """Enhanced day backup result with detailed tracking."""
    day: int
    date: datetime.date
    status: BackupStatus
    attempts: int = 0
    max_attempts: int = 3
    tables_processed: int = 0
    tables_successful: int = 0
    tables_failed: int = 0
    total_rows: int = 0
    duration_seconds: float = 0.0
    error_details: Optional[Dict[str, Any]] = None
    retry_delay: float = 0.0
    resource_metrics: Optional[Dict[str, float]] = None


@dataclass
class ResourceMetrics:
    """System resource metrics for monitoring."""
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    timestamp: datetime.datetime
    
    def is_healthy(self, thresholds: Dict[str, float]) -> bool:
        """Check if resources are within healthy thresholds."""
        return (
            self.cpu_percent < thresholds.get('cpu_percent', 70) and
            self.memory_percent < thresholds.get('memory_percent', 60) and
            self.disk_percent < thresholds.get('disk_percent', 80)
        )


class EnhancedMonthlyBackupProcessor:
    """
    Enhanced monthly backup processor with production-ready features.
    
    CRITICAL FIXES:
    - Automatic retry mechanism for failed operations
    - Dynamic resource management and throttling
    - Intelligent failure analysis and recovery
    - Real-time monitoring and alerting
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the enhanced processor."""
        self.config_manager = ConfigManager()
        self.config_path = config_path
        self.day_results: Dict[int, DayBackupResult] = {}
        self.resource_monitor = ResourceMonitor()
        self.failure_analyzer = FailureAnalyzer()
        self.checkpoint_manager = CheckpointManager()
        
        # Load enhanced configuration
        self.monthly_config = self.config_manager.get('backup', 'monthly_backup', {})
        self.resource_config = self.monthly_config.get('resource_management', {})
        self.error_config = self.monthly_config.get('error_recovery', {})
        self.health_config = self.monthly_config.get('health_checks', {})
        
        # Initialize monitoring
        self.start_time = datetime.datetime.now()
        self.total_operations = 0
        self.successful_operations = 0
        self.failed_operations = 0
        
        logger.info("Enhanced Monthly Backup Processor initialized")
        logger.info(f"Resource management: {self.resource_config.get('dynamic_delays', False)}")
        logger.info(f"Auto retry: {self.error_config.get('auto_retry_failed_days', False)}")
        logger.info(f"Health checks: {self.health_config.get('enabled', False)}")

    def process_monthly_backup(self, month_name: str, year: int, 
                             dry_run: bool = False, verbose: bool = False,
                             force_recovery: bool = False) -> Dict[str, Any]:
        """
        Process enhanced monthly backup with all critical fixes.
        
        Args:
            month_name: Name or number of month
            year: Year to backup
            dry_run: If True, validate only
            verbose: Enable verbose logging
            force_recovery: Force recovery mode
            
        Returns:
            Comprehensive backup results
        """
        logger.info("=" * 60)
        logger.info("ENHANCED MONTHLY BACKUP PROCESSOR - STARTING")
        logger.info("=" * 60)
        logger.info(f"Target: {month_name} {year}")
        logger.info(f"Mode: {'DRY RUN' if dry_run else 'PRODUCTION'}")
        logger.info(f"Force Recovery: {force_recovery}")
        logger.info(f"Start Time: {self.start_time}")
        
        try:
            # CRITICAL FIX 1: Pre-backup health checks
            if self.health_config.get('enabled', True):
                health_status = self._perform_health_checks()
                if not health_status['healthy'] and not force_recovery:
                    return {
                        'status': 'failed',
                        'error': 'Health checks failed',
                        'health_status': health_status,
                        'recommendation': 'Fix health issues or use --force-recovery'
                    }
            
            # Parse month and validate
            month_num = self._parse_month(month_name)
            if not month_num:
                return {'status': 'failed', 'error': f'Invalid month: {month_name}'}
            
            # Get days in month
            days_in_month = self._get_days_in_month(month_num, year)
            
            # Load table configuration with fallbacks
            table_names = self._load_table_configuration()
            if not table_names:
                return {'status': 'failed', 'error': 'No table configuration found'}
            
            logger.info(f"Processing {days_in_month} days for {len(table_names)} tables")
            
            # CRITICAL FIX 2: Use dedicated monthly backup processor
            monthly_processor = MonthlyBackupProcessor(self.config_manager)
            result = monthly_processor.process_monthly_backup(
                month=month_num,
                year=year,
                table_names=table_names,
                dry_run=dry_run
            )

            return result
                
        except Exception as e:
            error_details = handle_error(e, "Enhanced monthly backup processing")
            return {
                'status': 'critical_failure',
                'error': str(e),
                'error_details': error_details,
                'duration': (datetime.datetime.now() - self.start_time).total_seconds()
            }

    def _perform_health_checks(self) -> Dict[str, Any]:
        """Perform comprehensive health checks."""
        logger.info("Performing system health checks...")
        
        health_status = {
            'healthy': True,
            'checks': {},
            'warnings': [],
            'errors': []
        }
        
        try:
            # Check system resources
            metrics = self.resource_monitor.get_current_metrics()
            thresholds = self.config_manager.get('performance_monitoring', 'alert_thresholds', {})
            
            health_status['checks']['resources'] = {
                'cpu_percent': metrics.cpu_percent,
                'memory_percent': metrics.memory_percent,
                'disk_percent': metrics.disk_percent,
                'healthy': metrics.is_healthy(thresholds)
            }
            
            if not metrics.is_healthy(thresholds):
                health_status['healthy'] = False
                health_status['errors'].append(f"Resource thresholds exceeded: CPU {metrics.cpu_percent}%, Memory {metrics.memory_percent}%, Disk {metrics.disk_percent}%")
            
            # Check disk space
            free_space_gb = psutil.disk_usage('.').free / (1024**3)
            health_status['checks']['disk_space'] = {
                'free_gb': round(free_space_gb, 1),
                'healthy': free_space_gb > 5.0
            }
            
            if free_space_gb < 5.0:
                health_status['healthy'] = False
                health_status['errors'].append(f"Insufficient disk space: {free_space_gb:.1f} GB free")
            elif free_space_gb < 10.0:
                health_status['warnings'].append(f"Low disk space warning: {free_space_gb:.1f} GB free")
            
            # Check configuration files
            config_status = self._validate_configuration_files()
            health_status['checks']['configuration'] = config_status
            
            if not config_status['valid']:
                health_status['healthy'] = False
                health_status['errors'].extend(config_status['errors'])
            
            logger.info(f"Health check completed: {'✅ HEALTHY' if health_status['healthy'] else '❌ UNHEALTHY'}")
            
        except Exception as e:
            health_status['healthy'] = False
            health_status['errors'].append(f"Health check failed: {str(e)}")
            logger.error(f"Health check error: {str(e)}")
        
        return health_status

    def _load_table_configuration(self) -> List[str]:
        """Load table configuration with fallback support."""
        config_paths = self.monthly_config.get('configuration', {}).get('table_config_paths', [
            'tabletest/tables.json',
            'config/tables.json', 
            'backup/tables.json'
        ])
        
        fallback_tables = self.monthly_config.get('configuration', {}).get('fallback_table_list', [])
        
        for config_path in config_paths:
            try:
                if os.path.exists(config_path):
                    with open(config_path, 'r') as f:
                        tables = json.load(f)
                    if isinstance(tables, list) and tables:
                        logger.info(f"Loaded {len(tables)} tables from {config_path}")
                        return tables
            except Exception as e:
                logger.warning(f"Failed to load config from {config_path}: {str(e)}")
                continue
        
        if fallback_tables:
            logger.warning(f"Using fallback table list with {len(fallback_tables)} tables")
            return fallback_tables
        
        logger.error("No table configuration found in any location")
        return []

    def _validate_configuration_files(self) -> Dict[str, Any]:
        """Validate configuration files."""
        status = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Check main config
        try:
            config_data = self.config_manager.config
            if not config_data:
                status['valid'] = False
                status['errors'].append("Main configuration is empty")
        except Exception as e:
            status['valid'] = False
            status['errors'].append(f"Main configuration error: {str(e)}")
        
        # Check table configuration
        table_config_found = False
        config_paths = self.monthly_config.get('configuration', {}).get('table_config_paths', [
            'tabletest/tables.json'
        ])
        
        for path in config_paths:
            if os.path.exists(path):
                table_config_found = True
                try:
                    with open(path, 'r') as f:
                        json.load(f)
                except Exception as e:
                    status['valid'] = False
                    status['errors'].append(f"Invalid JSON in {path}: {str(e)}")
                break
        
        if not table_config_found:
            status['warnings'].append("No table configuration file found, will use fallback")
        
        return status


class ResourceMonitor:
    """Real-time resource monitoring for adaptive throttling."""
    
    def __init__(self):
        self.monitoring = False
        self.metrics_history = []
        self.alert_callbacks = []
    
    def get_current_metrics(self) -> ResourceMetrics:
        """Get current system resource metrics."""
        return ResourceMetrics(
            cpu_percent=psutil.cpu_percent(interval=1),
            memory_percent=psutil.virtual_memory().percent,
            disk_percent=psutil.disk_usage('.').percent,
            timestamp=datetime.datetime.now()
        )
    
    def calculate_adaptive_delay(self, base_delay: float, metrics: ResourceMetrics, 
                               thresholds: Dict[str, float]) -> float:
        """Calculate adaptive delay based on system load."""
        if metrics.is_healthy(thresholds):
            return base_delay
        
        # Increase delay based on resource pressure
        cpu_factor = max(1.0, metrics.cpu_percent / thresholds.get('cpu_percent', 70))
        memory_factor = max(1.0, metrics.memory_percent / thresholds.get('memory_percent', 60))
        disk_factor = max(1.0, metrics.disk_percent / thresholds.get('disk_percent', 80))
        
        adaptive_factor = max(cpu_factor, memory_factor, disk_factor)
        adaptive_delay = min(base_delay * adaptive_factor, 60.0)  # Cap at 60 seconds
        
        return adaptive_delay


class FailureAnalyzer:
    """Intelligent failure analysis for root cause identification."""
    
    def analyze_failure(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze failure and provide recommendations."""
        analysis = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context,
            'root_cause': 'unknown',
            'recommendations': [],
            'retry_recommended': False,
            'escalation_required': False
        }
        
        # Analyze common failure patterns
        error_msg = str(error).lower()
        
        if 'connection' in error_msg or 'timeout' in error_msg:
            analysis['root_cause'] = 'network_connectivity'
            analysis['recommendations'].append('Check network connectivity')
            analysis['recommendations'].append('Verify database server status')
            analysis['retry_recommended'] = True
        
        elif 'disk' in error_msg or 'space' in error_msg:
            analysis['root_cause'] = 'disk_space'
            analysis['recommendations'].append('Free up disk space')
            analysis['recommendations'].append('Clean up temporary files')
            analysis['escalation_required'] = True
        
        elif 'memory' in error_msg or 'out of memory' in error_msg:
            analysis['root_cause'] = 'memory_exhaustion'
            analysis['recommendations'].append('Reduce chunk size')
            analysis['recommendations'].append('Restart backup process')
            analysis['retry_recommended'] = True
        
        elif 'permission' in error_msg or 'access' in error_msg:
            analysis['root_cause'] = 'permission_denied'
            analysis['recommendations'].append('Check file permissions')
            analysis['recommendations'].append('Verify user credentials')
            analysis['escalation_required'] = True
        
        else:
            analysis['retry_recommended'] = True
            analysis['recommendations'].append('Generic retry recommended')
        
        return analysis


class CheckpointManager:
    """Enhanced checkpoint management for recovery."""
    
    def __init__(self):
        self.checkpoint_dir = Path('logs/checkpoints')
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
    
    def save_checkpoint(self, month: int, year: int, day_results: Dict[int, DayBackupResult]):
        """Save enhanced checkpoint with detailed state."""
        checkpoint_file = self.checkpoint_dir / f"monthly_backup_{year}_{month:02d}.json"
        
        checkpoint_data = {
            'timestamp': datetime.datetime.now().isoformat(),
            'month': month,
            'year': year,
            'day_results': {
                str(day): {
                    'status': result.status.value,
                    'attempts': result.attempts,
                    'tables_successful': result.tables_successful,
                    'tables_failed': result.tables_failed,
                    'duration_seconds': result.duration_seconds
                }
                for day, result in day_results.items()
            }
        }
        
        with open(checkpoint_file, 'w') as f:
            json.dump(checkpoint_data, f, indent=2)
    
    def load_checkpoint(self, month: int, year: int) -> Optional[Dict[str, Any]]:
        """Load checkpoint for recovery."""
        checkpoint_file = self.checkpoint_dir / f"monthly_backup_{year}_{month:02d}.json"

        if checkpoint_file.exists():
            try:
                with open(checkpoint_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load checkpoint: {str(e)}")

        return None


    def _parse_month(self, month_name: str) -> Optional[int]:
        """Parse month name or number to month number."""
        if month_name.isdigit():
            month_num = int(month_name)
            return month_num if 1 <= month_num <= 12 else None

        month_names = {
            'january': 1, 'february': 2, 'march': 3, 'april': 4,
            'may': 5, 'june': 6, 'july': 7, 'august': 8,
            'september': 9, 'october': 10, 'november': 11, 'december': 12
        }
        return month_names.get(month_name.lower())

    def _run_enhanced_backup(self, month_num: int, year: int, days_in_month: int,
                           table_names: List[str], force_recovery: bool) -> Dict[str, Any]:
        """Run enhanced backup with all critical fixes."""
        logger.info("Starting enhanced backup process...")

        # Initialize day results
        for day in range(1, days_in_month + 1):
            self.day_results[day] = DayBackupResult(
                day=day,
                date=datetime.date(year, month_num, day),
                status=BackupStatus.NOT_STARTED
            )

        # Load checkpoint for recovery
        checkpoint = self.checkpoint_manager.load_checkpoint(month_num, year)
        if checkpoint and not force_recovery:
            logger.info("Found existing checkpoint, resuming from last position")
            self._apply_checkpoint(checkpoint)

        successful_days = 0
        failed_days = 0

        # Process each day with enhanced error recovery
        for day in range(1, days_in_month + 1):
            day_result = self.day_results[day]

            if day_result.status == BackupStatus.COMPLETED:
                successful_days += 1
                continue

            logger.info(f"Processing day {day} of {days_in_month}")

            # CRITICAL FIX: Enhanced day processing with retries
            day_success = self._process_day_with_retries(day_result, table_names)

            if day_success:
                successful_days += 1
            else:
                failed_days += 1

            # Save checkpoint after each day
            self.checkpoint_manager.save_checkpoint(month_num, year, self.day_results)

            # CRITICAL FIX: Dynamic resource management
            if self.resource_config.get('dynamic_delays', True):
                self._apply_adaptive_delay(day_result)

        # Compile final results
        return {
            'status': 'completed' if failed_days == 0 else 'partial_failure',
            'month': month_num,
            'year': year,
            'total_days': days_in_month,
            'successful_days': successful_days,
            'failed_days': failed_days,
            'total_tables': len(table_names) * days_in_month,
            'duration_seconds': (datetime.datetime.now() - self.start_time).total_seconds(),
            'day_results': {day: result.__dict__ for day, result in self.day_results.items()}
        }

    def _apply_checkpoint(self, checkpoint: Dict[str, Any]):
        """Apply checkpoint data for recovery."""
        day_results = checkpoint.get('day_results', {})
        for day_str, result_data in day_results.items():
            day = int(day_str)
            if day in self.day_results:
                self.day_results[day].status = BackupStatus(result_data.get('status', 'not_started'))
                self.day_results[day].attempts = result_data.get('attempts', 0)
                self.day_results[day].tables_successful = result_data.get('tables_successful', 0)
                self.day_results[day].tables_failed = result_data.get('tables_failed', 0)

    def _process_day_with_retries(self, day_result: DayBackupResult, table_names: List[str]) -> bool:
        """Process a single day with enhanced retry mechanism."""
        max_attempts = self.error_config.get('max_day_retries', 3)

        while day_result.attempts < max_attempts:
            day_result.attempts += 1
            day_result.status = BackupStatus.IN_PROGRESS

            try:
                logger.info(f"Processing day {day_result.day}, attempt {day_result.attempts}")

                # Create backup configuration for this day
                config = BackupConfig()
                config.specific_date = day_result.date

                # Process tables for this day
                processor = UnifiedTableProcessor(config, strategy='basic')

                day_result.tables_successful = 0
                day_result.tables_failed = 0

                for table_name in table_names:
                    try:
                        result = processor.backup_table(table_name, mode='daily')
                        if result.get('status') == 'success':
                            day_result.tables_successful += 1
                            day_result.total_rows += result.get('total_rows', 0)
                        else:
                            day_result.tables_failed += 1
                    except Exception as e:
                        logger.error(f"Table {table_name} failed for day {day_result.day}: {str(e)}")
                        day_result.tables_failed += 1

                day_result.tables_processed = len(table_names)

                # Check if day was successful
                if day_result.tables_failed == 0:
                    day_result.status = BackupStatus.COMPLETED
                    logger.info(f"Day {day_result.day} completed successfully")
                    return True
                else:
                    day_result.status = BackupStatus.FAILED
                    logger.warning(f"Day {day_result.day} had {day_result.tables_failed} failed tables")

            except Exception as e:
                day_result.status = BackupStatus.FAILED
                day_result.error_details = self.failure_analyzer.analyze_failure(e, {
                    'day': day_result.day,
                    'attempt': day_result.attempts
                })
                logger.error(f"Day {day_result.day} attempt {day_result.attempts} failed: {str(e)}")

            # Apply exponential backoff if retrying
            if day_result.attempts < max_attempts and self.error_config.get('exponential_backoff', True):
                backoff_delay = self._calculate_backoff_delay(day_result.attempts)
                logger.info(f"Waiting {backoff_delay} seconds before retry...")
                time.sleep(backoff_delay)

        # All attempts failed
        day_result.status = BackupStatus.CRITICAL_FAILURE
        logger.error(f"Day {day_result.day} failed after {max_attempts} attempts")
        return False

    def _calculate_backoff_delay(self, attempt: int) -> float:
        """Calculate exponential backoff delay."""
        base_delay = self.error_config.get('retry_delay_minutes', 5) * 60
        multiplier = self.error_config.get('backoff_multiplier', 2)
        max_delay = self.error_config.get('max_backoff_seconds', 300)

        delay = min(base_delay * (multiplier ** (attempt - 1)), max_delay)
        return delay

    def _apply_adaptive_delay(self, day_result: DayBackupResult):
        """Apply adaptive delay based on system resources."""
        if not self.resource_config.get('adaptive_throttling', True):
            return

        metrics = self.resource_monitor.get_current_metrics()
        thresholds = self.config_manager.get('performance_monitoring', 'alert_thresholds', {})

        base_delay = self.resource_config.get('min_delay_seconds', 2)
        adaptive_delay = self.resource_monitor.calculate_adaptive_delay(base_delay, metrics, thresholds)

        if adaptive_delay > base_delay:
            logger.info(f"Applying adaptive delay: {adaptive_delay:.1f} seconds (system load detected)")
            time.sleep(adaptive_delay)
        else:
            time.sleep(base_delay)

    # Add methods to the class
    EnhancedMonthlyBackupProcessor._parse_month = _parse_month
    EnhancedMonthlyBackupProcessor._get_days_in_month = _get_days_in_month
    EnhancedMonthlyBackupProcessor._run_enhanced_dry_run = _run_enhanced_dry_run
    EnhancedMonthlyBackupProcessor._run_enhanced_backup = _run_enhanced_backup
    EnhancedMonthlyBackupProcessor._apply_checkpoint = _apply_checkpoint
    EnhancedMonthlyBackupProcessor._process_day_with_retries = _process_day_with_retries
    EnhancedMonthlyBackupProcessor._calculate_backoff_delay = _calculate_backoff_delay
    EnhancedMonthlyBackupProcessor._apply_adaptive_delay = _apply_adaptive_delay

# Apply the missing methods
_add_missing_methods()


def main():
    """Main entry point for enhanced monthly backup processor."""
    parser = argparse.ArgumentParser(description='Enhanced Monthly Backup Processor')
    parser.add_argument('--month', required=True, help='Month name or number')
    parser.add_argument('--year', type=int, required=True, help='Year')
    parser.add_argument('--config-path', help='Path to table configuration file')
    parser.add_argument('--dry-run', action='store_true', help='Validate only')
    parser.add_argument('--verbose', action='store_true', help='Verbose logging')
    parser.add_argument('--force-recovery', action='store_true', help='Force recovery mode')
    
    args = parser.parse_args()
    
    # Initialize processor
    processor = EnhancedMonthlyBackupProcessor(args.config_path)
    
    # Run backup
    result = processor.process_monthly_backup(
        args.month, args.year, args.dry_run, args.verbose, args.force_recovery
    )
    
    # Print results
    print(f"Backup completed with status: {result.get('status', 'unknown')}")
    if result.get('error'):
        print(f"Error: {result['error']}")
    
    # Exit with appropriate code
    sys.exit(0 if result.get('status') == 'completed' else 1)


if __name__ == '__main__':
    main()
